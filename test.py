import os
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
from openai import OpenAI, BadRequestError  # 确保导入所有需要的模块

client = OpenAI(
    api_key="sk-64e26a3cc8e644d49987870a2cdcf4b8",  # 如果您没有配置环境变量，请在此处替换您的API-KEY
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",  # 填写DashScope服务base_url
)
try:
    # 初始化messages列表
    completion = client.chat.completions.create(
        model="qwen-long",
        messages=[
            {'role': 'system', 'content': '你是一位专业的Excel数据处理专家，擅长将复杂的数据需求拆分为清晰的步骤并准确执行。技能包括：1.需求解析与拆分（输出步骤编号、参数说明和处理逻辑）2.数据映射确认（确认Excel列名对应关系）3.执行（直接返回处理结果）4.数据验证与反馈（提供验证结果和反馈）。限制：确保步骤清晰、确认数据来源、严格按顺序执行、及时询问不明确信息、返回完整数据且避免重复上传Excel。'},
            # 请将 'file-fe-xxx'替换为您实际对话场景所使用的 fileid。
            {'role': 'system', 'content': f'fileid://file-fe-869a3bbd25704714bed4888e'},
            {'role': 'user', 'content': '对excel文件进行数据过滤，CV最终更新日往后推90天取得CV过期日如果小于当日日期则保留数据，否则排除'}
        ],
        # 所有代码示例均采用流式输出，以清晰和直观地展示模型输出过程。如果您希望查看非流式输出的案例，请参见https://help.aliyun.com/zh/model-studio/text-generation
        stream=True,
        stream_options={"include_usage": True}
    )

    full_content = ""
    for chunk in completion:
        if chunk.choices and chunk.choices[0].delta.content:
            # 拼接输出内容
            full_content += chunk.choices[0].delta.content
            print(chunk.model_dump())

    print(full_content)

except BadRequestError as e:
    print(f"错误信息：{e}")
    print("请参考文档：https://help.aliyun.com/zh/model-studio/developer-reference/error-code")